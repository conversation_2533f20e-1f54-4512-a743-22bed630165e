// Shopping cart functionality
let cart = JSON.parse(localStorage.getItem('cart')) || [];

// Add item to cart
function addToCart(productId) {
    const product = products.find(p => p.id === productId);
    if (!product || product.stock === 0) {
        alert('Product is out of stock!');
        return;
    }

    const existingItem = cart.find(item => item.id === productId);
    
    if (existingItem) {
        if (existingItem.quantity < product.stock) {
            existingItem.quantity += 1;
            showNotification('Item quantity updated in cart!');
        } else {
            alert('Cannot add more items. Stock limit reached!');
            return;
        }
    } else {
        cart.push({
            id: product.id,
            name: product.name,
            price: product.price,
            image: product.image,
            quantity: 1,
            maxStock: product.stock
        });
        showNotification('Item added to cart!');
    }
    
    updateCartCount();
    saveCart();
}

// Remove item from cart
function removeFromCart(productId) {
    cart = cart.filter(item => item.id !== productId);
    updateCartDisplay();
    updateCartCount();
    saveCart();
    showNotification('Item removed from cart!');
}

// Update item quantity
function updateQuantity(productId, change) {
    const item = cart.find(item => item.id === productId);
    if (!item) return;
    
    const newQuantity = item.quantity + change;
    
    if (newQuantity <= 0) {
        removeFromCart(productId);
        return;
    }
    
    if (newQuantity > item.maxStock) {
        alert('Cannot add more items. Stock limit reached!');
        return;
    }
    
    item.quantity = newQuantity;
    updateCartDisplay();
    updateCartCount();
    saveCart();
}

// Toggle cart modal
function toggleCart() {
    const cartModal = document.getElementById('cartModal');
    const isVisible = cartModal.style.display === 'block';
    
    if (isVisible) {
        cartModal.style.display = 'none';
    } else {
        updateCartDisplay();
        cartModal.style.display = 'block';
    }
}

// Update cart display
function updateCartDisplay() {
    const cartItems = document.getElementById('cartItems');
    const cartTotal = document.getElementById('cartTotal');
    
    if (cart.length === 0) {
        cartItems.innerHTML = '<p style="text-align: center; padding: 20px; color: #666;">Your cart is empty</p>';
        cartTotal.textContent = '0.00';
        return;
    }
    
    cartItems.innerHTML = '';
    let total = 0;
    
    cart.forEach(item => {
        const itemTotal = item.price * item.quantity;
        total += itemTotal;
        
        const cartItem = document.createElement('div');
        cartItem.className = 'cart-item';
        cartItem.innerHTML = `
            <img src="${item.image}" alt="${item.name}" onerror="this.src='https://via.placeholder.com/60x60?text=No+Image'">
            <div class="cart-item-info">
                <div class="cart-item-name">${item.name}</div>
                <div class="cart-item-price">$${item.price.toFixed(2)} each</div>
                <div class="quantity-controls">
                    <button class="quantity-btn" onclick="updateQuantity(${item.id}, -1)">-</button>
                    <span>Qty: ${item.quantity}</span>
                    <button class="quantity-btn" onclick="updateQuantity(${item.id}, 1)">+</button>
                </div>
                <div style="font-weight: bold; color: #007bff;">Subtotal: $${itemTotal.toFixed(2)}</div>
            </div>
            <button class="action-btn delete" onclick="removeFromCart(${item.id})" title="Remove item">
                <i class="fas fa-trash"></i>
            </button>
        `;
        cartItems.appendChild(cartItem);
    });
    
    cartTotal.textContent = total.toFixed(2);
}

// Update cart count in navigation
function updateCartCount() {
    const cartCount = document.getElementById('cartCount');
    const totalItems = cart.reduce((sum, item) => sum + item.quantity, 0);
    cartCount.textContent = totalItems;
}

// Save cart to localStorage
function saveCart() {
    localStorage.setItem('cart', JSON.stringify(cart));
}

// Checkout function
function checkout() {
    if (cart.length === 0) {
        alert('Your cart is empty!');
        return;
    }
    
    const total = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
    
    // In a real application, you would integrate with a payment processor like Stripe
    const confirmPayment = confirm(`Total: $${total.toFixed(2)}\n\nProceed to payment?\n\n(This is a demo - no actual payment will be processed)`);
    
    if (confirmPayment) {
        // Simulate payment processing
        showPaymentModal(total);
    }
}

// Show payment modal (simplified for demo)
function showPaymentModal(total) {
    const paymentModal = document.createElement('div');
    paymentModal.className = 'modal';
    paymentModal.style.display = 'block';
    paymentModal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h3>Payment</h3>
                <span class="close" onclick="this.closest('.modal').remove()">&times;</span>
            </div>
            <div style="padding: 20px;">
                <h4>Order Total: $${total.toFixed(2)}</h4>
                <form id="paymentForm">
                    <input type="text" placeholder="Card Number (Demo: ****************)" required>
                    <div style="display: flex; gap: 10px;">
                        <input type="text" placeholder="MM/YY" style="width: 50%;" required>
                        <input type="text" placeholder="CVC" style="width: 50%;" required>
                    </div>
                    <input type="text" placeholder="Cardholder Name" required>
                    <button type="submit" style="background: #28a745;">Complete Payment</button>
                </form>
            </div>
        </div>
    `;
    
    document.body.appendChild(paymentModal);
    
    // Handle payment form submission
    paymentModal.querySelector('#paymentForm').addEventListener('submit', function(e) {
        e.preventDefault();
        processPayment(total);
        paymentModal.remove();
    });
}

// Process payment (demo)
function processPayment(total) {
    // Simulate payment processing delay
    const loadingModal = document.createElement('div');
    loadingModal.className = 'modal';
    loadingModal.style.display = 'block';
    loadingModal.innerHTML = `
        <div class="modal-content" style="text-align: center;">
            <div style="padding: 40px;">
                <i class="fas fa-spinner fa-spin" style="font-size: 3rem; color: #007bff;"></i>
                <h3>Processing Payment...</h3>
            </div>
        </div>
    `;
    document.body.appendChild(loadingModal);
    
    setTimeout(() => {
        loadingModal.remove();
        
        // Create order
        const order = {
            id: generateId(),
            items: [...cart],
            total: total,
            date: new Date().toISOString(),
            status: 'completed'
        };
        
        // Save order (in a real app, this would go to a database)
        let orders = JSON.parse(localStorage.getItem('orders')) || [];
        orders.push(order);
        localStorage.setItem('orders', JSON.stringify(orders));
        
        // Clear cart
        cart = [];
        saveCart();
        updateCartCount();
        toggleCart();
        
        // Show success message
        showSuccessModal(order.id);
    }, 2000);
}

// Show success modal
function showSuccessModal(orderId) {
    const successModal = document.createElement('div');
    successModal.className = 'modal';
    successModal.style.display = 'block';
    successModal.innerHTML = `
        <div class="modal-content" style="text-align: center;">
            <div style="padding: 40px;">
                <i class="fas fa-check-circle" style="font-size: 4rem; color: #28a745; margin-bottom: 20px;"></i>
                <h2>Payment Successful!</h2>
                <p>Your order has been placed successfully.</p>
                <p><strong>Order ID:</strong> ${orderId}</p>
                <button onclick="this.closest('.modal').remove()" style="margin-top: 20px; background: #007bff;">Continue Shopping</button>
            </div>
        </div>
    `;
    document.body.appendChild(successModal);
}

// Show notification
function showNotification(message, type = 'success') {
    const notification = document.createElement('div');

    // Determine notification style based on type
    const styles = {
        success: {
            background: 'linear-gradient(45deg, #2ecc71, #27ae60)',
            icon: '✅'
        },
        error: {
            background: 'linear-gradient(45deg, #e74c3c, #c0392b)',
            icon: '❌'
        },
        info: {
            background: 'linear-gradient(45deg, #3498db, #2980b9)',
            icon: 'ℹ️'
        },
        warning: {
            background: 'linear-gradient(45deg, #f39c12, #e67e22)',
            icon: '⚠️'
        }
    };

    const style = styles[type] || styles.success;

    notification.style.cssText = `
        position: fixed;
        top: 90px;
        right: 20px;
        background: ${style.background};
        color: white;
        padding: 18px 25px;
        border-radius: 15px;
        z-index: 3000;
        box-shadow: 0 8px 32px rgba(0,0,0,0.3);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255,255,255,0.2);
        font-weight: 600;
        font-size: 1rem;
        animation: slideInBounce 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);
        cursor: pointer;
        transition: all 0.3s ease;
    `;

    notification.innerHTML = `
        <span style="margin-right: 10px; font-size: 1.2rem;">${style.icon}</span>
        ${message}
    `;

    // Add animation styles if not already added
    if (!document.getElementById('notification-styles')) {
        const styleSheet = document.createElement('style');
        styleSheet.id = 'notification-styles';
        styleSheet.textContent = `
            @keyframes slideInBounce {
                0% {
                    transform: translateX(100%) scale(0.8);
                    opacity: 0;
                }
                60% {
                    transform: translateX(-10px) scale(1.05);
                    opacity: 1;
                }
                100% {
                    transform: translateX(0) scale(1);
                    opacity: 1;
                }
            }
            @keyframes slideOutBounce {
                0% {
                    transform: translateX(0) scale(1);
                    opacity: 1;
                }
                100% {
                    transform: translateX(100%) scale(0.8);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(styleSheet);
    }

    // Add hover effect
    notification.addEventListener('mouseenter', () => {
        notification.style.transform = 'scale(1.05)';
        notification.style.boxShadow = '0 12px 40px rgba(0,0,0,0.4)';
    });

    notification.addEventListener('mouseleave', () => {
        notification.style.transform = 'scale(1)';
        notification.style.boxShadow = '0 8px 32px rgba(0,0,0,0.3)';
    });

    // Click to dismiss
    notification.addEventListener('click', () => {
        notification.style.animation = 'slideOutBounce 0.3s ease-in-out forwards';
        setTimeout(() => notification.remove(), 300);
    });

    document.body.appendChild(notification);

    // Auto remove after 4 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.style.animation = 'slideOutBounce 0.3s ease-in-out forwards';
            setTimeout(() => notification.remove(), 300);
        }
    }, 4000);
}

// Export functions
window.addToCart = addToCart;
window.removeFromCart = removeFromCart;
window.updateQuantity = updateQuantity;
window.toggleCart = toggleCart;
window.checkout = checkout;
