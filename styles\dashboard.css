/* Dashboard Specific Styles */
.dashboard-container {
    display: flex;
    margin-top: 70px;
    min-height: calc(100vh - 70px);
}

/* Sidebar */
.sidebar {
    width: 250px;
    background: #2c3e50;
    color: white;
    padding: 20px 0;
}

.sidebar-menu {
    list-style: none;
}

.sidebar-menu li {
    margin-bottom: 5px;
}

.sidebar-menu a {
    display: block;
    padding: 15px 20px;
    color: #bdc3c7;
    text-decoration: none;
    transition: all 0.3s;
}

.sidebar-menu a:hover,
.sidebar-menu a.active {
    background: #34495e;
    color: white;
    border-left: 4px solid #3498db;
}

/* Main Content */
.main-content {
    flex: 1;
    padding: 30px;
    background: #f8f9fa;
}

.dashboard-section {
    display: none;
}

.dashboard-section.active {
    display: block;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
}

.section-header h2 {
    color: #2c3e50;
    font-size: 2rem;
}

.btn-primary {
    background: #3498db;
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 5px;
    cursor: pointer;
    font-size: 1rem;
    transition: background 0.3s;
}

.btn-primary:hover {
    background: #2980b9;
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 40px;
}

.stat-card {
    background: white;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    text-align: center;
    transition: transform 0.3s;
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-card i {
    font-size: 3rem;
    color: #3498db;
    margin-bottom: 15px;
}

.stat-card h3 {
    font-size: 2.5rem;
    color: #2c3e50;
    margin-bottom: 10px;
}

.stat-card p {
    color: #7f8c8d;
    font-size: 1.1rem;
}

/* Tables */
.products-table,
.orders-table {
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    overflow: hidden;
}

table {
    width: 100%;
    border-collapse: collapse;
}

th {
    background: #34495e;
    color: white;
    padding: 15px;
    text-align: left;
    font-weight: 600;
}

td {
    padding: 15px;
    border-bottom: 1px solid #ecf0f1;
}

tr:hover {
    background: #f8f9fa;
}

.product-image-small {
    width: 50px;
    height: 50px;
    object-fit: cover;
    border-radius: 5px;
}

.status-badge {
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: bold;
    text-transform: uppercase;
}

.status-pending {
    background: #f39c12;
    color: white;
}

.status-completed {
    background: #27ae60;
    color: white;
}

.status-cancelled {
    background: #e74c3c;
    color: white;
}

.action-btn {
    background: none;
    border: none;
    color: #3498db;
    cursor: pointer;
    margin-right: 10px;
    font-size: 1rem;
}

.action-btn:hover {
    color: #2980b9;
}

.action-btn.delete {
    color: #e74c3c;
}

.action-btn.delete:hover {
    color: #c0392b;
}

/* Form Grid */
.form-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group.full-width {
    grid-column: 1 / -1;
}

.form-group label {
    margin-bottom: 5px;
    font-weight: bold;
    color: #2c3e50;
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 15px;
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #eee;
}

.form-actions button {
    padding: 10px 20px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 1rem;
}

.form-actions button[type="button"] {
    background: #95a5a6;
    color: white;
}

.form-actions button[type="button"]:hover {
    background: #7f8c8d;
}

/* Analytics */
.analytics-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 30px;
}

.chart-container {
    background: white;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.chart-container h3 {
    margin-bottom: 20px;
    color: #2c3e50;
}

#topProducts {
    max-height: 300px;
    overflow-y: auto;
}

.top-product-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #ecf0f1;
}

.merchant-name {
    color: #2c3e50;
    font-weight: bold;
}

/* Responsive Design */
@media (max-width: 768px) {
    .dashboard-container {
        flex-direction: column;
    }
    
    .sidebar {
        width: 100%;
        order: 2;
    }
    
    .main-content {
        order: 1;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .form-grid {
        grid-template-columns: 1fr;
    }
    
    .analytics-grid {
        grid-template-columns: 1fr;
    }
    
    .section-header {
        flex-direction: column;
        gap: 15px;
        align-items: flex-start;
    }
}
