/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f8f9fa;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Navigation */
.navbar {
    background: #fff;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 70px;
}

.nav-logo h2 {
    color: #007bff;
    font-size: 1.8rem;
}

.nav-menu {
    display: flex;
    align-items: center;
    gap: 30px;
}

.nav-link {
    text-decoration: none;
    color: #333;
    font-weight: 500;
    transition: color 0.3s;
}

.nav-link:hover {
    color: #007bff;
}

.cart-icon {
    position: relative;
    cursor: pointer;
    font-size: 1.5rem;
    color: #333;
}

.cart-count {
    position: absolute;
    top: -8px;
    right: -8px;
    background: #dc3545;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
}

/* Hero Section */
.hero {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    padding: 150px 0 100px;
    text-align: center;
}

.hero-content h1 {
    font-size: 3rem;
    margin-bottom: 20px;
}

.hero-content p {
    font-size: 1.2rem;
    margin-bottom: 30px;
}

.cta-button {
    background: #fff;
    color: #007bff;
    padding: 15px 30px;
    border: none;
    border-radius: 5px;
    font-size: 1.1rem;
    font-weight: bold;
    cursor: pointer;
    transition: transform 0.3s;
}

.cta-button:hover {
    transform: translateY(-2px);
}

/* Products Section */
.products-section {
    padding: 80px 0;
}

.products-section h2 {
    text-align: center;
    margin-bottom: 50px;
    font-size: 2.5rem;
    color: #333;
}

.search-filter {
    display: flex;
    gap: 20px;
    margin-bottom: 40px;
    justify-content: center;
}

.search-filter input,
.search-filter select {
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 1rem;
}

.search-filter input {
    width: 300px;
}

.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 30px;
}

.product-card {
    background: white;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    overflow: hidden;
    transition: transform 0.3s;
}

.product-card:hover {
    transform: translateY(-5px);
}

.product-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.product-info {
    padding: 20px;
}

.product-name {
    font-size: 1.2rem;
    font-weight: bold;
    margin-bottom: 10px;
}

.product-price {
    color: #007bff;
    font-size: 1.3rem;
    font-weight: bold;
    margin-bottom: 10px;
}

.product-description {
    color: #666;
    margin-bottom: 15px;
    font-size: 0.9rem;
}

.add-to-cart-btn {
    width: 100%;
    background: #007bff;
    color: white;
    border: none;
    padding: 12px;
    border-radius: 5px;
    cursor: pointer;
    font-size: 1rem;
    transition: background 0.3s;
}

.add-to-cart-btn:hover {
    background: #0056b3;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
}

.modal-content {
    background-color: white;
    margin: 5% auto;
    padding: 0;
    border-radius: 10px;
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-content.large {
    max-width: 800px;
}

.modal-header {
    padding: 20px;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.close {
    font-size: 20px;
    font-weight: bold;
    cursor: pointer;
    color: #666;
    background: #f8f9fa;
    border: 1px solid #ddd;
    border-radius: 50%;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    line-height: 1;
    padding: 0;
}

.close:hover {
    color: #fff;
    background: #dc3545;
    border-color: #dc3545;
    transform: scale(1.1);
}

/* Cart Styles */
.cart-items {
    padding: 20px;
    max-height: 400px;
    overflow-y: auto;
}

.cart-item {
    display: flex;
    align-items: center;
    padding: 15px 0;
    border-bottom: 1px solid #eee;
}

.cart-item img {
    width: 60px;
    height: 60px;
    object-fit: cover;
    border-radius: 5px;
    margin-right: 15px;
}

.cart-item-info {
    flex: 1;
}

.cart-item-name {
    font-weight: bold;
    margin-bottom: 5px;
}

.cart-item-price {
    color: #007bff;
    font-weight: bold;
}

.quantity-controls {
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 10px 0;
}

.quantity-btn {
    background: #f8f9fa;
    border: 1px solid #ddd;
    width: 30px;
    height: 30px;
    border-radius: 3px;
    cursor: pointer;
}

.cart-total {
    padding: 20px;
    border-top: 1px solid #eee;
    text-align: center;
}

.checkout-btn {
    background: #28a745;
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 5px;
    font-size: 1.1rem;
    cursor: pointer;
    margin-top: 15px;
}

.checkout-btn:hover {
    background: #218838;
}

/* Form Styles */
form {
    padding: 20px;
}

form input,
form select,
form textarea {
    width: 100%;
    padding: 12px;
    margin-bottom: 15px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 1rem;
}

form button {
    width: 100%;
    background: #007bff;
    color: white;
    border: none;
    padding: 12px;
    border-radius: 5px;
    font-size: 1rem;
    cursor: pointer;
}

form button:hover {
    background: #0056b3;
}

/* Footer */
.footer {
    background: #333;
    color: white;
    text-align: center;
    padding: 30px 0;
    margin-top: 50px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-menu {
        gap: 15px;
    }
    
    .hero-content h1 {
        font-size: 2rem;
    }
    
    .search-filter {
        flex-direction: column;
        align-items: center;
    }
    
    .search-filter input {
        width: 100%;
        max-width: 300px;
    }
    
    .products-grid {
        grid-template-columns: 1fr;
    }
}
