/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #2c3e50;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    overflow-x: hidden;
}

/* Smooth scrolling */
html {
    scroll-behavior: smooth;
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(45deg, #667eea, #764ba2);
    border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(45deg, #5a6fd8, #6a4190);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Navigation */
.navbar {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
    transition: all 0.3s ease;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.navbar:hover {
    background: rgba(255, 255, 255, 0.98);
    box-shadow: 0 12px 40px rgba(0,0,0,0.15);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 70px;
}

.nav-logo h2 {
    background: linear-gradient(45deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-size: 1.8rem;
    font-weight: 700;
    letter-spacing: -0.5px;
    transition: all 0.3s ease;
}

.nav-logo h2:hover {
    transform: scale(1.05);
}

.nav-menu {
    display: flex;
    align-items: center;
    gap: 30px;
}

.nav-link {
    text-decoration: none;
    color: #2c3e50;
    font-weight: 500;
    padding: 8px 16px;
    border-radius: 25px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, #667eea, #764ba2);
    transition: left 0.3s ease;
    z-index: -1;
    border-radius: 25px;
}

.nav-link:hover {
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.nav-link:hover::before {
    left: 0;
}

.cart-icon {
    position: relative;
    cursor: pointer;
    font-size: 1.5rem;
    color: #2c3e50;
    padding: 10px;
    border-radius: 50%;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.1);
}

.cart-icon:hover {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    transform: scale(1.1) rotate(5deg);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.cart-count {
    position: absolute;
    top: -2px;
    right: -2px;
    background: linear-gradient(45deg, #ff6b6b, #ee5a24);
    color: white;
    border-radius: 50%;
    width: 22px;
    height: 22px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    font-weight: bold;
    animation: pulse 2s infinite;
    box-shadow: 0 2px 10px rgba(255, 107, 107, 0.4);
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

/* Hero Section */
.hero {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
    color: white;
    padding: 150px 0 100px;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 100" fill="rgba(255,255,255,0.1)"><polygon points="1000,100 1000,0 0,100"/></svg>');
    background-size: cover;
    animation: float 6s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.hero-content {
    position: relative;
    z-index: 2;
}

.hero-content h1 {
    font-size: 3.5rem;
    margin-bottom: 20px;
    font-weight: 700;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    animation: slideInDown 1s ease-out;
}

.hero-content p {
    font-size: 1.3rem;
    margin-bottom: 40px;
    opacity: 0.9;
    animation: slideInUp 1s ease-out 0.3s both;
}

.cta-button {
    background: linear-gradient(45deg, #ff6b6b, #ee5a24);
    color: white;
    padding: 18px 40px;
    border: none;
    border-radius: 50px;
    font-size: 1.1rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 8px 25px rgba(255, 107, 107, 0.3);
    position: relative;
    overflow: hidden;
    animation: slideInUp 1s ease-out 0.6s both;
}

.cta-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, #ee5a24, #ff6b6b);
    transition: left 0.3s ease;
    z-index: -1;
}

.cta-button:hover {
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 12px 35px rgba(255, 107, 107, 0.4);
}

.cta-button:hover::before {
    left: 0;
}

@keyframes slideInDown {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Products Section */
.products-section {
    padding: 80px 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    position: relative;
}

.products-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, #667eea, transparent);
}

.products-section h2 {
    text-align: center;
    margin-bottom: 60px;
    font-size: 2.8rem;
    background: linear-gradient(45deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
    position: relative;
}

.products-section h2::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: linear-gradient(45deg, #667eea, #764ba2);
    border-radius: 2px;
}

.search-filter {
    display: flex;
    gap: 20px;
    margin-bottom: 50px;
    justify-content: center;
    flex-wrap: wrap;
}

.search-filter input,
.search-filter select {
    padding: 15px 20px;
    border: 2px solid transparent;
    border-radius: 25px;
    font-size: 1rem;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.search-filter input:focus,
.search-filter select:focus {
    outline: none;
    border-color: #667eea;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.2);
}

.search-filter input {
    width: 320px;
    background: rgba(255, 255, 255, 0.9);
}

.search-filter input::placeholder {
    color: #666;
}

.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 35px;
    padding: 0 20px;
}

.product-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
    overflow: hidden;
    transition: all 0.4s ease;
    position: relative;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.product-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(45deg, #667eea, #764ba2);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.product-card:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: 0 20px 60px rgba(102, 126, 234, 0.2);
}

.product-card:hover::before {
    transform: scaleX(1);
}

.product-image-container {
    position: relative;
    overflow: hidden;
    border-radius: 20px 20px 0 0;
}

.product-image {
    width: 100%;
    height: 220px;
    object-fit: cover;
    transition: transform 0.4s ease;
}

.product-card:hover .product-image {
    transform: scale(1.1);
}

.stock-indicator {
    position: absolute;
    top: 15px;
    right: 15px;
    padding: 8px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: bold;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    animation: pulse 2s infinite;
}

.stock-indicator.in-stock {
    background: rgba(46, 204, 113, 0.9);
    color: white;
}

.stock-indicator.low-stock {
    background: rgba(241, 196, 15, 0.9);
    color: white;
}

.stock-indicator.out-of-stock {
    background: rgba(231, 76, 60, 0.9);
    color: white;
}

.product-info {
    padding: 25px;
    position: relative;
}

.product-name {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 12px;
    color: #2c3e50;
    line-height: 1.4;
}

.product-price {
    background: linear-gradient(45deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-size: 1.4rem;
    font-weight: 700;
    margin-bottom: 12px;
}

.product-description {
    color: #666;
    margin-bottom: 20px;
    font-size: 0.95rem;
    line-height: 1.5;
}

.add-to-cart-btn {
    width: 100%;
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    border: none;
    padding: 15px;
    border-radius: 25px;
    cursor: pointer;
    font-size: 1rem;
    font-weight: 600;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.add-to-cart-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, #764ba2, #667eea);
    transition: left 0.3s ease;
    z-index: -1;
}

.add-to-cart-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.add-to-cart-btn:hover::before {
    left: 0;
}

.add-to-cart-btn:active {
    transform: translateY(0);
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.6);
    backdrop-filter: blur(5px);
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.modal-content {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    margin: 3% auto;
    padding: 0;
    border-radius: 20px;
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 20px 60px rgba(0,0,0,0.3);
    border: 1px solid rgba(255, 255, 255, 0.2);
    animation: slideIn 0.4s ease;
    position: relative;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.modal-content.large {
    max-width: 900px;
}

.modal-content::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(45deg, #667eea, #764ba2);
    border-radius: 20px 20px 0 0;
}

.modal-header {
    padding: 25px 30px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
}

.modal-header h3 {
    background: linear-gradient(45deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 600;
    font-size: 1.4rem;
}

.close {
    font-size: 20px;
    font-weight: bold;
    cursor: pointer;
    color: #666;
    background: #f8f9fa;
    border: 1px solid #ddd;
    border-radius: 50%;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    line-height: 1;
    padding: 0;
}

.close:hover {
    color: #fff;
    background: #dc3545;
    border-color: #dc3545;
    transform: scale(1.1);
}

/* Cart Styles */
.cart-items {
    padding: 20px;
    max-height: 400px;
    overflow-y: auto;
}

.cart-item {
    display: flex;
    align-items: center;
    padding: 15px 0;
    border-bottom: 1px solid #eee;
}

.cart-item img {
    width: 60px;
    height: 60px;
    object-fit: cover;
    border-radius: 5px;
    margin-right: 15px;
}

.cart-item-info {
    flex: 1;
}

.cart-item-name {
    font-weight: bold;
    margin-bottom: 5px;
}

.cart-item-price {
    color: #007bff;
    font-weight: bold;
}

.quantity-controls {
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 10px 0;
}

.quantity-btn {
    background: #f8f9fa;
    border: 1px solid #ddd;
    width: 30px;
    height: 30px;
    border-radius: 3px;
    cursor: pointer;
}

.cart-total {
    padding: 20px;
    border-top: 1px solid #eee;
    text-align: center;
}

.checkout-btn {
    background: #28a745;
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 5px;
    font-size: 1.1rem;
    cursor: pointer;
    margin-top: 15px;
}

.checkout-btn:hover {
    background: #218838;
}

/* Form Styles */
form {
    padding: 30px;
}

form input,
form select,
form textarea {
    width: 100%;
    padding: 15px 20px;
    margin-bottom: 20px;
    border: 2px solid transparent;
    border-radius: 15px;
    font-size: 1rem;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

form input:focus,
form select:focus,
form textarea:focus {
    outline: none;
    border-color: #667eea;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.2);
    background: rgba(255, 255, 255, 1);
}

form input::placeholder,
form textarea::placeholder {
    color: #999;
    transition: color 0.3s ease;
}

form input:focus::placeholder,
form textarea:focus::placeholder {
    color: #667eea;
}

form button {
    width: 100%;
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    border: none;
    padding: 15px;
    border-radius: 25px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    position: relative;
    overflow: hidden;
}

form button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, #764ba2, #667eea);
    transition: left 0.3s ease;
    z-index: -1;
}

form button:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

form button:hover::before {
    left: 0;
}

/* Footer */
.footer {
    background: linear-gradient(135deg, #2c3e50, #34495e);
    color: white;
    text-align: center;
    padding: 40px 0;
    margin-top: 80px;
    position: relative;
    overflow: hidden;
}

.footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, #667eea, transparent);
}

.footer p {
    font-size: 1.1rem;
    opacity: 0.9;
    margin: 0;
    position: relative;
    z-index: 2;
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Hover Effects for Interactive Elements */
.interactive-hover {
    transition: all 0.3s ease;
}

.interactive-hover:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

/* Floating Animation */
.float {
    animation: float 3s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

/* Glow Effect */
.glow {
    box-shadow: 0 0 20px rgba(102, 126, 234, 0.3);
    animation: glow 2s ease-in-out infinite alternate;
}

@keyframes glow {
    from { box-shadow: 0 0 20px rgba(102, 126, 234, 0.3); }
    to { box-shadow: 0 0 30px rgba(102, 126, 234, 0.6); }
}

/* Ripple Effect */
.ripple {
    position: absolute;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.6);
    transform: scale(0);
    animation: ripple-animation 0.6s linear;
    pointer-events: none;
}

@keyframes ripple-animation {
    to {
        transform: scale(4);
        opacity: 0;
    }
}

/* Button and Interactive Elements */
button, .nav-link, .product-card {
    position: relative;
    overflow: hidden;
}

/* Smooth Transitions */
* {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Focus States */
button:focus,
input:focus,
select:focus,
textarea:focus {
    outline: 2px solid rgba(102, 126, 234, 0.5);
    outline-offset: 2px;
}

/* Selection Colors */
::selection {
    background: rgba(102, 126, 234, 0.3);
    color: #2c3e50;
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-menu {
        gap: 10px;
        flex-wrap: wrap;
    }

    .nav-link {
        padding: 6px 12px;
        font-size: 0.9rem;
    }

    .hero-content h1 {
        font-size: 2.5rem;
    }

    .hero-content p {
        font-size: 1.1rem;
    }

    .search-filter {
        flex-direction: column;
        align-items: center;
        gap: 15px;
    }

    .search-filter input {
        width: 100%;
        max-width: 300px;
    }

    .products-grid {
        grid-template-columns: 1fr;
        gap: 25px;
    }

    .product-card {
        margin: 0 10px;
    }

    .modal-content {
        width: 95%;
        margin: 10% auto;
    }

    form {
        padding: 20px;
    }
}

@media (max-width: 480px) {
    .hero-content h1 {
        font-size: 2rem;
    }

    .products-section h2 {
        font-size: 2.2rem;
    }

    .nav-container {
        padding: 0 15px;
    }

    .container {
        padding: 0 15px;
    }
}
