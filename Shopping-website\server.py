#!/usr/bin/env python3
"""
Simple HTTP server for testing the shopping website
"""

import http.server
import socketserver
import os
import webbrowser
from urllib.parse import urlparse

class CustomHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    def end_headers(self):
        # Add CORS headers for local development
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        super().end_headers()
    
    def do_GET(self):
        # Parse the URL
        parsed_path = urlparse(self.path)
        
        # If requesting root, serve index.html
        if parsed_path.path == '/':
            self.path = '/index.html'
        
        # Serve the file
        return super().do_GET()

def start_server(port=8000):
    """Start the HTTP server"""
    
    # Change to the directory containing the website files
    os.chdir(os.path.dirname(os.path.abspath(__file__)))
    
    # Create server
    with socketserver.TCPServer(("", port), CustomHTTPRequestHandler) as httpd:
        print(f"Shopping website server running at http://localhost:{port}")
        print("Press Ctrl+C to stop the server")
        
        try:
            # Open browser automatically
            webbrowser.open(f'http://localhost:{port}')
            
            # Start serving
            httpd.serve_forever()
        except KeyboardInterrupt:
            print("\nServer stopped.")
            httpd.shutdown()

if __name__ == "__main__":
    start_server()
