// Shopping cart functionality
let cart = JSON.parse(localStorage.getItem('cart')) || [];

// Add item to cart
function addToCart(productId) {
    const product = products.find(p => p.id === productId);
    if (!product || product.stock === 0) {
        alert('Product is out of stock!');
        return;
    }

    const existingItem = cart.find(item => item.id === productId);
    
    if (existingItem) {
        if (existingItem.quantity < product.stock) {
            existingItem.quantity += 1;
            showNotification('Item quantity updated in cart!');
        } else {
            alert('Cannot add more items. Stock limit reached!');
            return;
        }
    } else {
        cart.push({
            id: product.id,
            name: product.name,
            price: product.price,
            image: product.image,
            quantity: 1,
            maxStock: product.stock
        });
        showNotification('Item added to cart!');
    }
    
    updateCartCount();
    saveCart();
}

// Remove item from cart
function removeFromCart(productId) {
    cart = cart.filter(item => item.id !== productId);
    updateCartDisplay();
    updateCartCount();
    saveCart();
    showNotification('Item removed from cart!');
}

// Update item quantity
function updateQuantity(productId, change) {
    const item = cart.find(item => item.id === productId);
    if (!item) return;
    
    const newQuantity = item.quantity + change;
    
    if (newQuantity <= 0) {
        removeFromCart(productId);
        return;
    }
    
    if (newQuantity > item.maxStock) {
        alert('Cannot add more items. Stock limit reached!');
        return;
    }
    
    item.quantity = newQuantity;
    updateCartDisplay();
    updateCartCount();
    saveCart();
}

// Toggle cart modal
function toggleCart() {
    const cartModal = document.getElementById('cartModal');
    const isVisible = cartModal.style.display === 'block';
    
    if (isVisible) {
        cartModal.style.display = 'none';
    } else {
        updateCartDisplay();
        cartModal.style.display = 'block';
    }
}

// Update cart display
function updateCartDisplay() {
    const cartItems = document.getElementById('cartItems');
    const cartTotal = document.getElementById('cartTotal');
    
    if (cart.length === 0) {
        cartItems.innerHTML = '<p style="text-align: center; padding: 20px; color: #666;">Your cart is empty</p>';
        cartTotal.textContent = '0.00';
        return;
    }
    
    cartItems.innerHTML = '';
    let total = 0;
    
    cart.forEach(item => {
        const itemTotal = item.price * item.quantity;
        total += itemTotal;
        
        const cartItem = document.createElement('div');
        cartItem.className = 'cart-item';
        cartItem.innerHTML = `
            <img src="${item.image}" alt="${item.name}" onerror="this.src='https://via.placeholder.com/60x60?text=No+Image'">
            <div class="cart-item-info">
                <div class="cart-item-name">${item.name}</div>
                <div class="cart-item-price">$${item.price.toFixed(2)} each</div>
                <div class="quantity-controls">
                    <button class="quantity-btn" onclick="updateQuantity(${item.id}, -1)">-</button>
                    <span>Qty: ${item.quantity}</span>
                    <button class="quantity-btn" onclick="updateQuantity(${item.id}, 1)">+</button>
                </div>
                <div style="font-weight: bold; color: #007bff;">Subtotal: $${itemTotal.toFixed(2)}</div>
            </div>
            <button class="action-btn delete" onclick="removeFromCart(${item.id})" title="Remove item">
                <i class="fas fa-trash"></i>
            </button>
        `;
        cartItems.appendChild(cartItem);
    });
    
    cartTotal.textContent = total.toFixed(2);
}

// Update cart count in navigation
function updateCartCount() {
    const cartCount = document.getElementById('cartCount');
    const totalItems = cart.reduce((sum, item) => sum + item.quantity, 0);
    cartCount.textContent = totalItems;
}

// Save cart to localStorage
function saveCart() {
    localStorage.setItem('cart', JSON.stringify(cart));
}

// Checkout function
function checkout() {
    if (cart.length === 0) {
        alert('Your cart is empty!');
        return;
    }
    
    const total = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
    
    // In a real application, you would integrate with a payment processor like Stripe
    const confirmPayment = confirm(`Total: $${total.toFixed(2)}\n\nProceed to payment?\n\n(This is a demo - no actual payment will be processed)`);
    
    if (confirmPayment) {
        // Simulate payment processing
        showPaymentModal(total);
    }
}

// Show payment modal (simplified for demo)
function showPaymentModal(total) {
    const paymentModal = document.createElement('div');
    paymentModal.className = 'modal';
    paymentModal.style.display = 'block';
    paymentModal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h3>Payment</h3>
                <span class="close" onclick="this.closest('.modal').remove()">&times;</span>
            </div>
            <div style="padding: 20px;">
                <h4>Order Total: $${total.toFixed(2)}</h4>
                <form id="paymentForm">
                    <input type="text" placeholder="Card Number (Demo: ****************)" required>
                    <div style="display: flex; gap: 10px;">
                        <input type="text" placeholder="MM/YY" style="width: 50%;" required>
                        <input type="text" placeholder="CVC" style="width: 50%;" required>
                    </div>
                    <input type="text" placeholder="Cardholder Name" required>
                    <button type="submit" style="background: #28a745;">Complete Payment</button>
                </form>
            </div>
        </div>
    `;
    
    document.body.appendChild(paymentModal);
    
    // Handle payment form submission
    paymentModal.querySelector('#paymentForm').addEventListener('submit', function(e) {
        e.preventDefault();
        processPayment(total);
        paymentModal.remove();
    });
}

// Process payment (demo)
function processPayment(total) {
    // Simulate payment processing delay
    const loadingModal = document.createElement('div');
    loadingModal.className = 'modal';
    loadingModal.style.display = 'block';
    loadingModal.innerHTML = `
        <div class="modal-content" style="text-align: center;">
            <div style="padding: 40px;">
                <i class="fas fa-spinner fa-spin" style="font-size: 3rem; color: #007bff;"></i>
                <h3>Processing Payment...</h3>
            </div>
        </div>
    `;
    document.body.appendChild(loadingModal);
    
    setTimeout(() => {
        loadingModal.remove();
        
        // Create order
        const order = {
            id: generateId(),
            items: [...cart],
            total: total,
            date: new Date().toISOString(),
            status: 'completed'
        };
        
        // Save order (in a real app, this would go to a database)
        let orders = JSON.parse(localStorage.getItem('orders')) || [];
        orders.push(order);
        localStorage.setItem('orders', JSON.stringify(orders));
        
        // Clear cart
        cart = [];
        saveCart();
        updateCartCount();
        toggleCart();
        
        // Show success message
        showSuccessModal(order.id);
    }, 2000);
}

// Show success modal
function showSuccessModal(orderId) {
    const successModal = document.createElement('div');
    successModal.className = 'modal';
    successModal.style.display = 'block';
    successModal.innerHTML = `
        <div class="modal-content" style="text-align: center;">
            <div style="padding: 40px;">
                <i class="fas fa-check-circle" style="font-size: 4rem; color: #28a745; margin-bottom: 20px;"></i>
                <h2>Payment Successful!</h2>
                <p>Your order has been placed successfully.</p>
                <p><strong>Order ID:</strong> ${orderId}</p>
                <button onclick="this.closest('.modal').remove()" style="margin-top: 20px; background: #007bff;">Continue Shopping</button>
            </div>
        </div>
    `;
    document.body.appendChild(successModal);
}

// Show notification
function showNotification(message) {
    const notification = document.createElement('div');
    notification.style.cssText = `
        position: fixed;
        top: 90px;
        right: 20px;
        background: #28a745;
        color: white;
        padding: 15px 20px;
        border-radius: 5px;
        z-index: 3000;
        animation: slideIn 0.3s ease;
    `;
    notification.textContent = message;
    
    // Add animation styles
    const style = document.createElement('style');
    style.textContent = `
        @keyframes slideIn {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
    `;
    document.head.appendChild(style);
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.remove();
        style.remove();
    }, 3000);
}

// Export functions
window.addToCart = addToCart;
window.removeFromCart = removeFromCart;
window.updateQuantity = updateQuantity;
window.toggleCart = toggleCart;
window.checkout = checkout;
