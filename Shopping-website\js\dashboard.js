// Merchant Dashboard functionality
let merchantProducts = [];
let merchantOrders = [];

// Initialize dashboard
document.addEventListener('DOMContentLoaded', function() {
    if (!isMerchantAuthenticated()) {
        window.location.href = 'index.html';
        return;
    }

    loadMerchantData();
    showSection('overview');
    updateDashboardStats();
    setupDragAndDrop();

    // Add event listener for the Add Product button as backup
    const addProductBtn = document.getElementById('addProductBtn');
    if (addProductBtn) {
        addProductBtn.addEventListener('click', function(e) {
            e.preventDefault();
            showAddProductModal();
        });
    }

    // Setup form handler
    setupFormHandler();
});

// Setup drag and drop functionality
function setupDragAndDrop() {
    // We'll set this up when the modal is shown since the elements don't exist initially
}

// Setup drag and drop for the upload area (called when modal is shown)
function initializeDragAndDrop() {
    const uploadArea = document.querySelector('.upload-area');
    if (!uploadArea || uploadArea.dataset.dragSetup) return;

    // Mark as setup to avoid duplicate event listeners
    uploadArea.dataset.dragSetup = 'true';

    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
        uploadArea.addEventListener(eventName, preventDefaults, false);
    });

    function preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }

    ['dragenter', 'dragover'].forEach(eventName => {
        uploadArea.addEventListener(eventName, highlight, false);
    });

    ['dragleave', 'drop'].forEach(eventName => {
        uploadArea.addEventListener(eventName, unhighlight, false);
    });

    function highlight(e) {
        uploadArea.classList.add('drag-over');
    }

    function unhighlight(e) {
        uploadArea.classList.remove('drag-over');
    }

    uploadArea.addEventListener('drop', handleDrop, false);

    function handleDrop(e) {
        const dt = e.dataTransfer;
        const files = dt.files;

        if (files.length > 0) {
            const fileInput = document.querySelector('input[name="imageFile"]');
            try {
                // Try to use DataTransfer (modern browsers)
                const dataTransfer = new DataTransfer();
                dataTransfer.items.add(files[0]);
                fileInput.files = dataTransfer.files;
            } catch (error) {
                // Fallback: directly process the file without setting input.files
                console.log('DataTransfer not supported, processing file directly');
            }

            // Process the file directly from the drop event
            const fakeInput = { files: [files[0]] };
            handleImageUpload(fakeInput);
        }
    }
}

// Load merchant-specific data
function loadMerchantData() {
    const currentMerchant = getCurrentMerchant();
    if (!currentMerchant) return;

    // Initialize products array if it doesn't exist
    if (!window.products || !Array.isArray(window.products)) {
        console.log('Initializing products array from localStorage or creating new one');
        const storedProducts = localStorage.getItem('products');
        if (storedProducts) {
            try {
                window.products = JSON.parse(storedProducts);
                console.log('Loaded products from localStorage:', window.products.length);
            } catch (e) {
                console.error('Error parsing stored products:', e);
                window.products = [];
            }
        } else {
            window.products = [];
            console.log('Created new empty products array');
        }
    }

    // Load products for current merchant
    merchantProducts = window.products.filter(p => p.merchantId === currentMerchant.id);
    console.log('Loaded', merchantProducts.length, 'products for merchant', currentMerchant.id);

    // Load orders for current merchant
    const allOrders = JSON.parse(localStorage.getItem('orders')) || [];
    merchantOrders = allOrders.filter(order =>
        order.items.some(item =>
            merchantProducts.some(product => product.id === item.id)
        )
    );
}

// Show different dashboard sections
function showSection(sectionName, clickedElement) {
    // Hide all sections
    document.querySelectorAll('.dashboard-section').forEach(section => {
        section.classList.remove('active');
    });

    // Remove active class from all menu items
    document.querySelectorAll('.sidebar-menu a').forEach(link => {
        link.classList.remove('active');
    });

    // Show selected section
    const targetSection = document.getElementById(sectionName);
    if (targetSection) {
        targetSection.classList.add('active');
    }

    // Add active class to clicked menu item (if provided)
    if (clickedElement) {
        clickedElement.classList.add('active');
    } else {
        // Find the menu item for this section and make it active
        const menuLink = document.querySelector(`a[onclick*="showSection('${sectionName}')"]`);
        if (menuLink) {
            menuLink.classList.add('active');
        }
    }

    // Load section-specific data
    switch(sectionName) {
        case 'products':
            loadProductsTable();
            break;
        case 'orders':
            loadOrdersTable();
            break;
        case 'analytics':
            loadAnalytics();
            break;
    }
}

// Update dashboard statistics
function updateDashboardStats() {
    const totalProducts = merchantProducts.length;
    const totalOrders = merchantOrders.length;
    const totalRevenue = merchantOrders.reduce((sum, order) => sum + order.total, 0);
    const totalCustomers = new Set(merchantOrders.map(order => order.customerId || 'guest')).size;
    
    document.getElementById('totalProducts').textContent = totalProducts;
    document.getElementById('totalOrders').textContent = totalOrders;
    document.getElementById('totalRevenue').textContent = `$${totalRevenue.toFixed(2)}`;
    document.getElementById('totalCustomers').textContent = totalCustomers;
}

// Load products table
function loadProductsTable() {
    console.log('=== LOADING PRODUCTS TABLE ===');
    console.log('Merchant products to display:', merchantProducts.length);

    const tbody = document.getElementById('productsTableBody');
    if (!tbody) {
        console.error('Products table body not found!');
        return;
    }

    tbody.innerHTML = '';

    console.log('Products to display:', merchantProducts);

    merchantProducts.forEach((product, index) => {
        console.log(`Adding product ${index + 1}:`, product);
        const row = document.createElement('tr');
        row.innerHTML = `
            <td><img src="${product.image}" alt="${product.name}" class="product-image-small" onerror="this.src='https://via.placeholder.com/50x50?text=No+Image'"></td>
            <td>${product.name}</td>
            <td>${product.category}</td>
            <td>$${product.price.toFixed(2)}</td>
            <td>${product.stock}</td>
            <td>
                <button class="action-btn" onclick="editProduct(${product.id})" title="Edit">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="action-btn delete" onclick="deleteProduct(${product.id})" title="Delete">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        `;
        tbody.appendChild(row);
    });
}

// Load orders table
function loadOrdersTable() {
    const tbody = document.getElementById('ordersTableBody');
    tbody.innerHTML = '';
    
    merchantOrders.forEach(order => {
        const row = document.createElement('tr');
        const orderDate = new Date(order.date).toLocaleDateString();
        
        row.innerHTML = `
            <td>#${order.id.substr(-8)}</td>
            <td>Guest Customer</td>
            <td>${orderDate}</td>
            <td>$${order.total.toFixed(2)}</td>
            <td><span class="status-badge status-${order.status}">${order.status}</span></td>
            <td>
                <button class="action-btn" onclick="viewOrder('${order.id}')" title="View Details">
                    <i class="fas fa-eye"></i>
                </button>
                <button class="action-btn" onclick="updateOrderStatus('${order.id}')" title="Update Status">
                    <i class="fas fa-edit"></i>
                </button>
            </td>
        `;
        tbody.appendChild(row);
    });
}

// Show add product modal
function showAddProductModal() {
    console.log('showAddProductModal called'); // Debug log
    const modal = document.getElementById('addProductModal');
    if (modal) {
        modal.style.display = 'block';
        // Initialize drag and drop after modal is shown
        setTimeout(() => {
            initializeDragAndDrop();
        }, 100);
    } else {
        console.error('Modal not found: addProductModal');
    }
}

// Close modal function
function closeModal(modalId) {
    document.getElementById(modalId).style.display = 'none';
}

// Close modal when clicking outside
document.addEventListener('click', function(event) {
    const modals = document.querySelectorAll('.modal');
    modals.forEach(modal => {
        if (event.target === modal) {
            modal.style.display = 'none';
            // Reset form if it's the add product modal
            if (modal.id === 'addProductModal') {
                resetProductForm();
            }
        }
    });
});

// Setup form handler
function setupFormHandler() {
    const form = document.getElementById('addProductForm');
    if (form && !form.dataset.handlerAttached) {
        // Mark as having handler attached to prevent duplicates
        form.dataset.handlerAttached = 'true';

        form.addEventListener('submit', function(e) {
            e.preventDefault();
            console.log('=== FORM SUBMISSION STARTED ===');
            console.log('Form submitted!'); // Debug log

            const formData = new FormData(this);
            const currentMerchant = getCurrentMerchant();

            console.log('Current merchant:', currentMerchant); // Debug log
            console.log('Form data entries:');
            for (let [key, value] of formData.entries()) {
                console.log(key + ':', value);
            }

            if (!currentMerchant) {
                console.error('No merchant logged in!');
                alert('Error: No merchant logged in');
                return;
            }

            // Get image source (either uploaded file or URL)
            let imageSource = getImageSource();
            console.log('Image source:', imageSource); // Debug log

            // Validate form data
            const name = formData.get('name');
            const category = formData.get('category');
            const price = formData.get('price');
            const stock = formData.get('stock');
            const description = formData.get('description');

            console.log('Form validation:');
            console.log('Name:', name);
            console.log('Category:', category);
            console.log('Price:', price);
            console.log('Stock:', stock);
            console.log('Description:', description);

            if (!name || !category || !price || !stock || !description) {
                console.error('Validation failed - missing fields');
                alert('Please fill in all required fields');
                return;
            }

            console.log('Validation passed!');

            const newProduct = {
                id: Date.now(), // Simple ID generation
                name: name.trim(),
                category: category,
                price: parseFloat(price),
                stock: parseInt(stock),
                description: description.trim(),
                image: imageSource || 'https://via.placeholder.com/300x200?text=No+Image',
                merchantId: currentMerchant.id
            };

            console.log('New product:', newProduct); // Debug log

            // Check if we're editing an existing product
            const editingId = this.dataset.editingId;
            if (editingId) {
                // Update existing product
                const productIndex = window.products.findIndex(p => p.id == editingId);
                const merchantIndex = merchantProducts.findIndex(p => p.id == editingId);

                if (productIndex > -1) {
                    window.products[productIndex] = { ...window.products[productIndex], ...newProduct, id: parseInt(editingId) };
                }
                if (merchantIndex > -1) {
                    merchantProducts[merchantIndex] = { ...merchantProducts[merchantIndex], ...newProduct, id: parseInt(editingId) };
                }

                // Reset editing state
                delete this.dataset.editingId;
                document.querySelector('#addProductModal .modal-header h3').textContent = 'Add New Product';
                document.querySelector('#addProductForm button[type="submit"]').textContent = 'Add Product';

                showNotification('Product updated successfully!');
            } else {
                // Add new product
                console.log('=== ADDING NEW PRODUCT ===');
                console.log('Product to add:', newProduct);
                console.log('Current products array length:', window.products ? window.products.length : 'undefined');
                console.log('Current merchant products length:', merchantProducts.length);

                // Ensure products array exists and is an array
                if (!window.products || !Array.isArray(window.products)) {
                    console.log('Initializing products array...');
                    const storedProducts = localStorage.getItem('products');
                    if (storedProducts) {
                        try {
                            window.products = JSON.parse(storedProducts);
                            console.log('Loaded from localStorage:', window.products.length, 'products');
                        } catch (e) {
                            console.error('Error parsing stored products:', e);
                            window.products = [];
                        }
                    } else {
                        window.products = [];
                        console.log('Created new empty products array');
                    }
                }

                window.products.push(newProduct);
                merchantProducts.push(newProduct);

                console.log('After adding - products array length:', window.products.length);
                console.log('After adding - merchant products length:', merchantProducts.length);

                showNotification('Product added successfully!');
            }

            // Save to localStorage (in a real app, this would go to a database)
            console.log('Saving to localStorage...');
            localStorage.setItem('products', JSON.stringify(window.products));
            console.log('Saved to localStorage');

            // Refresh the products table
            console.log('Refreshing products table...');
            loadProductsTable();
            updateDashboardStats();
            console.log('Table refreshed');

            // Close modal and reset form
            console.log('Closing modal...');
            closeModal('addProductModal');
            resetProductForm();
            console.log('=== FORM SUBMISSION COMPLETED ===');
        });
    } else {
        console.error('Form not found: addProductForm');
    }
}

// Image handling functions
function switchImageTab(tabType) {
    // Remove active class from all tabs
    document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
    document.querySelectorAll('.image-input-tab').forEach(tab => tab.classList.remove('active'));

    // Add active class to selected tab
    document.querySelector(`button[onclick="switchImageTab('${tabType}')"]`).classList.add('active');
    document.getElementById(tabType === 'upload' ? 'uploadTab' : 'urlTab').classList.add('active');
}

function handleImageUpload(input) {
    const file = input.files[0];
    if (!file) return;

    // Validate file type
    if (!file.type.startsWith('image/')) {
        alert('Please select a valid image file');
        input.value = '';
        return;
    }

    // Validate file size (5MB limit)
    if (file.size > 5 * 1024 * 1024) {
        alert('Image size should be less than 5MB');
        input.value = '';
        return;
    }

    // Create preview
    const reader = new FileReader();
    reader.onload = function(e) {
        showImagePreview(e.target.result);
    };
    reader.readAsDataURL(file);
}

function handleUrlChange(input) {
    const url = input.value.trim();
    if (!url) {
        removeImagePreview();
        return;
    }

    // Basic URL validation
    try {
        new URL(url);
        // Test if the URL points to an image by trying to load it
        const img = new Image();
        img.onload = function() {
            showImagePreview(url);
        };
        img.onerror = function() {
            alert('Unable to load image from the provided URL. Please check the URL and try again.');
            removeImagePreview();
        };
        img.src = url;
    } catch (e) {
        alert('Please enter a valid URL');
        removeImagePreview();
    }
}

function showImagePreview(src) {
    const preview = document.getElementById('imagePreview');
    const img = document.getElementById('previewImg');

    img.src = src;
    preview.style.display = 'block';
}

function removeImagePreview() {
    const preview = document.getElementById('imagePreview');
    const fileInput = document.querySelector('input[name="imageFile"]');
    const urlInput = document.querySelector('input[name="imageUrl"]');

    preview.style.display = 'none';
    fileInput.value = '';
    urlInput.value = '';
}

function getImageSource() {
    const fileInput = document.querySelector('input[name="imageFile"]');
    const urlInput = document.querySelector('input[name="imageUrl"]');
    const previewImg = document.getElementById('previewImg');

    // If there's a file uploaded, return the data URL
    if (fileInput && fileInput.files && fileInput.files[0] && previewImg && previewImg.src) {
        return previewImg.src;
    }

    // Otherwise return the URL input value
    if (urlInput && urlInput.value) {
        return urlInput.value;
    }

    return '';
}

function resetProductForm() {
    const form = document.getElementById('addProductForm');
    if (form) {
        form.reset();
        removeImagePreview();
        switchImageTab('upload'); // Reset to upload tab

        // Reset modal title and button text
        const modalTitle = document.querySelector('#addProductModal .modal-header h3');
        const submitBtn = document.querySelector('#addProductForm button[type="submit"]');

        if (modalTitle) modalTitle.textContent = 'Add New Product';
        if (submitBtn) submitBtn.textContent = 'Add Product';

        // Clear editing state
        delete form.dataset.editingId;
    }
}

// Edit product
function editProduct(productId) {
    const product = merchantProducts.find(p => p.id === productId);
    if (!product) return;

    // Pre-fill the form with existing data
    const form = document.getElementById('addProductForm');
    form.name.value = product.name;
    form.category.value = product.category;
    form.price.value = product.price;
    form.stock.value = product.stock;
    form.description.value = product.description;

    // Handle image - check if it's a data URL or regular URL
    if (product.image.startsWith('data:')) {
        // It's an uploaded image
        switchImageTab('upload');
        showImagePreview(product.image);
    } else {
        // It's a URL
        switchImageTab('url');
        form.imageUrl.value = product.image;
        if (product.image && product.image !== 'https://via.placeholder.com/300x200?text=No+Image') {
            showImagePreview(product.image);
        }
    }

    // Change modal title and button text
    document.querySelector('#addProductModal .modal-header h3').textContent = 'Edit Product';
    document.querySelector('#addProductForm button[type="submit"]').textContent = 'Update Product';

    // Store the product ID for updating
    form.dataset.editingId = productId;

    showAddProductModal();
}

// Delete product
function deleteProduct(productId) {
    if (!confirm('Are you sure you want to delete this product?')) return;

    // Remove from global products array
    const globalIndex = window.products.findIndex(p => p.id === productId);
    if (globalIndex > -1) {
        window.products.splice(globalIndex, 1);
    }

    // Remove from merchant products
    const merchantIndex = merchantProducts.findIndex(p => p.id === productId);
    if (merchantIndex > -1) {
        merchantProducts.splice(merchantIndex, 1);
    }

    // Save to localStorage
    localStorage.setItem('products', JSON.stringify(window.products));

    // Refresh the table
    loadProductsTable();
    updateDashboardStats();

    showNotification('Product deleted successfully!');
}

// View order details
function viewOrder(orderId) {
    const order = merchantOrders.find(o => o.id === orderId);
    if (!order) return;
    
    const modal = document.createElement('div');
    modal.className = 'modal';
    modal.style.display = 'block';
    
    const itemsList = order.items.map(item => `
        <div style="display: flex; justify-content: space-between; padding: 10px 0; border-bottom: 1px solid #eee;">
            <span>${item.name}</span>
            <span>Qty: ${item.quantity} × $${item.price.toFixed(2)} = $${(item.quantity * item.price).toFixed(2)}</span>
        </div>
    `).join('');
    
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h3>Order Details - #${orderId.substr(-8)}</h3>
                <span class="close" onclick="this.closest('.modal').remove()">&times;</span>
            </div>
            <div style="padding: 20px;">
                <p><strong>Date:</strong> ${new Date(order.date).toLocaleString()}</p>
                <p><strong>Status:</strong> <span class="status-badge status-${order.status}">${order.status}</span></p>
                <h4>Items:</h4>
                <div>${itemsList}</div>
                <div style="text-align: right; margin-top: 20px; font-size: 1.2rem; font-weight: bold;">
                    Total: $${order.total.toFixed(2)}
                </div>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
}

// Update order status
function updateOrderStatus(orderId) {
    const newStatus = prompt('Enter new status (pending, completed, cancelled):');
    if (!newStatus || !['pending', 'completed', 'cancelled'].includes(newStatus)) {
        alert('Invalid status');
        return;
    }
    
    // Update in merchantOrders
    const order = merchantOrders.find(o => o.id === orderId);
    if (order) {
        order.status = newStatus;
    }
    
    // Update in localStorage
    let allOrders = JSON.parse(localStorage.getItem('orders')) || [];
    const orderIndex = allOrders.findIndex(o => o.id === orderId);
    if (orderIndex > -1) {
        allOrders[orderIndex].status = newStatus;
        localStorage.setItem('orders', JSON.stringify(allOrders));
    }
    
    loadOrdersTable();
    showNotification('Order status updated!');
}

// Load analytics
function loadAnalytics() {
    // Simple analytics - in a real app, you'd have more sophisticated charts
    const topProducts = merchantProducts
        .map(product => {
            const soldQuantity = merchantOrders.reduce((sum, order) => {
                const orderItem = order.items.find(item => item.id === product.id);
                return sum + (orderItem ? orderItem.quantity : 0);
            }, 0);
            return { ...product, soldQuantity };
        })
        .sort((a, b) => b.soldQuantity - a.soldQuantity)
        .slice(0, 5);
    
    const topProductsContainer = document.getElementById('topProducts');
    topProductsContainer.innerHTML = topProducts.map(product => `
        <div class="top-product-item">
            <span>${product.name}</span>
            <span>${product.soldQuantity} sold</span>
        </div>
    `).join('');
}

// Show notification (reuse from cart.js)
function showNotification(message) {
    const notification = document.createElement('div');
    notification.style.cssText = `
        position: fixed;
        top: 90px;
        right: 20px;
        background: #28a745;
        color: white;
        padding: 15px 20px;
        border-radius: 5px;
        z-index: 3000;
        animation: slideIn 0.3s ease;
    `;
    notification.textContent = message;

    document.body.appendChild(notification);

    setTimeout(() => {
        notification.remove();
    }, 3000);
}

// Test function for debugging - can be called from browser console
function testAddProduct() {
    console.log('=== TEST ADD PRODUCT STARTED ===');

    const currentMerchant = getCurrentMerchant();
    if (!currentMerchant) {
        console.error('No merchant logged in');
        alert('No merchant logged in');
        return;
    }

    console.log('Current merchant:', currentMerchant);

    // Ensure products array exists and is an array
    if (!window.products || !Array.isArray(window.products)) {
        console.log('Initializing products array...');
        const storedProducts = localStorage.getItem('products');
        if (storedProducts) {
            try {
                window.products = JSON.parse(storedProducts);
                console.log('Loaded from localStorage:', window.products.length, 'products');
            } catch (e) {
                console.error('Error parsing stored products:', e);
                window.products = [];
            }
        } else {
            window.products = [];
            console.log('Created new empty products array');
        }
    }

    console.log('Products array type:', typeof window.products);
    console.log('Products array is array:', Array.isArray(window.products));
    console.log('Current products count:', window.products.length);

    const testProduct = {
        id: Date.now(),
        name: 'Test Product ' + Date.now(),
        category: 'electronics',
        price: 99.99,
        stock: 10,
        description: 'This is a test product created from console',
        image: 'https://via.placeholder.com/300x200?text=Test+Product',
        merchantId: currentMerchant.id
    };

    console.log('Test product to add:', testProduct);

    try {
        window.products.push(testProduct);
        merchantProducts.push(testProduct);
        localStorage.setItem('products', JSON.stringify(window.products));

        console.log('Product added. New count:', window.products.length);

        loadProductsTable();
        updateDashboardStats();

        console.log('Test product added successfully:', testProduct);
        showNotification('Test product added successfully!');
        return testProduct;
    } catch (error) {
        console.error('Error adding test product:', error);
        console.error('Error details:', error.message);
        console.error('Stack trace:', error.stack);
        alert('Error adding test product: ' + error.message);
        return null;
    }
}

// Export functions
window.showSection = showSection;
window.showAddProductModal = showAddProductModal;
window.closeModal = closeModal;
window.editProduct = editProduct;
window.deleteProduct = deleteProduct;
window.viewOrder = viewOrder;
window.updateOrderStatus = updateOrderStatus;
window.switchImageTab = switchImageTab;
window.handleImageUpload = handleImageUpload;
window.handleUrlChange = handleUrlChange;
window.removeImagePreview = removeImagePreview;
window.testAddProduct = testAddProduct;
