<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Merchant Dashboard - ShopEasy</title>
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="styles/dashboard.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <h2>ShopEasy - Merchant</h2>
            </div>
            <div class="nav-menu">
                <span class="merchant-name" id="merchantName">Welcome, Merchant</span>
                <a href="index.html" class="nav-link">Back to Store</a>
                <a href="#" class="nav-link" onclick="logout()">Logout</a>
            </div>
        </div>
    </nav>

    <!-- Dashboard Content -->
    <div class="dashboard-container">
        <!-- Sidebar -->
        <div class="sidebar">
            <ul class="sidebar-menu">
                <li><a href="#" onclick="showSection('overview')" class="active">Overview</a></li>
                <li><a href="#" onclick="showSection('products')">Products</a></li>
                <li><a href="#" onclick="showSection('orders')">Orders</a></li>
                <li><a href="#" onclick="showSection('analytics')">Analytics</a></li>
            </ul>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Overview Section -->
            <div id="overview" class="dashboard-section active">
                <h2>Dashboard Overview</h2>
                <div class="stats-grid">
                    <div class="stat-card">
                        <i class="fas fa-box"></i>
                        <h3 id="totalProducts">0</h3>
                        <p>Total Products</p>
                    </div>
                    <div class="stat-card">
                        <i class="fas fa-shopping-cart"></i>
                        <h3 id="totalOrders">0</h3>
                        <p>Total Orders</p>
                    </div>
                    <div class="stat-card">
                        <i class="fas fa-dollar-sign"></i>
                        <h3 id="totalRevenue">$0</h3>
                        <p>Total Revenue</p>
                    </div>
                    <div class="stat-card">
                        <i class="fas fa-users"></i>
                        <h3 id="totalCustomers">0</h3>
                        <p>Customers</p>
                    </div>
                </div>
            </div>

            <!-- Products Section -->
            <div id="products" class="dashboard-section">
                <div class="section-header">
                    <h2>Product Management</h2>
                    <button class="btn-primary" onclick="showAddProductModal()">Add New Product</button>
                </div>
                <div class="products-table">
                    <table id="productsTable">
                        <thead>
                            <tr>
                                <th>Image</th>
                                <th>Name</th>
                                <th>Category</th>
                                <th>Price</th>
                                <th>Stock</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="productsTableBody">
                            <!-- Products will be loaded here -->
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Orders Section -->
            <div id="orders" class="dashboard-section">
                <h2>Order Management</h2>
                <div class="orders-table">
                    <table id="ordersTable">
                        <thead>
                            <tr>
                                <th>Order ID</th>
                                <th>Customer</th>
                                <th>Date</th>
                                <th>Total</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="ordersTableBody">
                            <!-- Orders will be loaded here -->
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Analytics Section -->
            <div id="analytics" class="dashboard-section">
                <h2>Analytics</h2>
                <div class="analytics-grid">
                    <div class="chart-container">
                        <h3>Sales Overview</h3>
                        <canvas id="salesChart"></canvas>
                    </div>
                    <div class="chart-container">
                        <h3>Top Products</h3>
                        <div id="topProducts"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Product Modal -->
    <div id="addProductModal" class="modal">
        <div class="modal-content large">
            <div class="modal-header">
                <h3>Add New Product</h3>
                <span class="close" onclick="closeModal('addProductModal')">&times;</span>
            </div>
            <form id="addProductForm">
                <div class="form-grid">
                    <div class="form-group">
                        <label>Product Name</label>
                        <input type="text" name="name" required>
                    </div>
                    <div class="form-group">
                        <label>Category</label>
                        <select name="category" required>
                            <option value="">Select Category</option>
                            <option value="electronics">Electronics</option>
                            <option value="clothing">Clothing</option>
                            <option value="books">Books</option>
                            <option value="home">Home & Garden</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Price ($)</label>
                        <input type="number" name="price" step="0.01" required>
                    </div>
                    <div class="form-group">
                        <label>Stock Quantity</label>
                        <input type="number" name="stock" required>
                    </div>
                    <div class="form-group full-width">
                        <label>Description</label>
                        <textarea name="description" rows="3" required></textarea>
                    </div>
                    <div class="form-group full-width">
                        <label>Product Image URL</label>
                        <input type="url" name="image" placeholder="https://example.com/image.jpg">
                    </div>
                </div>
                <div class="form-actions">
                    <button type="button" onclick="closeModal('addProductModal')">Cancel</button>
                    <button type="submit" class="btn-primary">Add Product</button>
                </div>
            </form>
        </div>
    </div>

    <script src="js/dashboard.js"></script>
    <script src="js/auth.js"></script>
</body>
</html>
