# ShopEasy - Shopping Website

A complete shopping website built with HTML, CSS, and JavaScript featuring merchant product management and integrated payment processing.

## Features

### For Customers
- **Product Browsing**: Browse products with search and category filtering
- **Shopping Cart**: Add/remove items, adjust quantities
- **Secure Checkout**: Integrated payment processing (demo mode)
- **Responsive Design**: Works on desktop and mobile devices
- **User Authentication**: Login/register system

### For Merchants
- **Dashboard**: Overview of sales, orders, and analytics
- **Product Management**: Easy add, edit, delete products
- **Inventory Tracking**: Stock management
- **Order Management**: View and update order status
- **Analytics**: Sales overview and top products

## Getting Started

### Method 1: Python Server (Recommended)
1. Make sure you have Python installed
2. Run the server:
   ```bash
   python server.py
   ```
3. The website will automatically open in your browser at `http://localhost:8000`

### Method 2: Direct File Access
1. Open `index.html` in your web browser
2. Note: Some features may not work due to CORS restrictions

## Demo Credentials

### Customer Login
- Email: `<EMAIL>`
- Password: `password123`

### Merchant Login
- Email: `<EMAIL>`
- Password: `merchant123`

## File Structure

```
├── index.html              # Main customer-facing page
├── merchant-dashboard.html # Merchant dashboard
├── styles/
│   ├── main.css           # Main stylesheet
│   └── dashboard.css      # Dashboard-specific styles
├── js/
│   ├── main.js           # Main functionality
│   ├── cart.js           # Shopping cart functionality
│   ├── auth.js           # Authentication system
│   └── dashboard.js      # Merchant dashboard functionality
├── server.py             # Python development server
└── README.md            # This file
```

## Key Features Implemented

### 1. Product Management
- Merchants can easily add new products through a user-friendly form
- Edit existing products with pre-filled forms
- Delete products with confirmation
- Real-time inventory tracking

### 2. Shopping Experience
- Product search and filtering by category
- Responsive product grid layout
- Shopping cart with quantity controls
- Persistent cart using localStorage

### 3. Payment Integration
- Demo payment processing with form validation
- Order confirmation and tracking
- Payment success notifications

### 4. Authentication System
- Separate login systems for customers and merchants
- Session management using localStorage
- Protected merchant dashboard

### 5. Order Management
- Order tracking for both customers and merchants
- Status updates (pending, completed, cancelled)
- Order history and details

## Technical Implementation

### Frontend Technologies
- **HTML5**: Semantic markup and structure
- **CSS3**: Modern styling with Flexbox and Grid
- **JavaScript (ES6+)**: Interactive functionality
- **Font Awesome**: Icons and visual elements

### Data Storage
- **localStorage**: Client-side data persistence
- **JSON**: Data format for products, orders, and users

### Responsive Design
- Mobile-first approach
- Flexible grid layouts
- Responsive navigation and forms

## Customization

### Adding New Products
1. Login as a merchant
2. Go to the Products section in the dashboard
3. Click "Add New Product"
4. Fill in the product details
5. Submit the form

### Modifying Styles
- Edit `styles/main.css` for general styling
- Edit `styles/dashboard.css` for dashboard-specific styles

### Adding New Features
- Main functionality: `js/main.js`
- Cart features: `js/cart.js`
- Authentication: `js/auth.js`
- Dashboard features: `js/dashboard.js`

## Future Enhancements

### Potential Improvements
1. **Real Database Integration**: Replace localStorage with a proper database
2. **Real Payment Processing**: Integrate with Stripe, PayPal, or similar
3. **Image Upload**: Allow merchants to upload product images
4. **Email Notifications**: Order confirmations and updates
5. **Advanced Analytics**: Charts and detailed reporting
6. **Multi-vendor Support**: Support for multiple merchants
7. **Product Reviews**: Customer review and rating system
8. **Wishlist**: Save products for later
9. **Advanced Search**: Filters by price, rating, etc.
10. **Admin Panel**: Site administration features

### Security Considerations
- Implement proper password hashing
- Add CSRF protection
- Validate all user inputs
- Use HTTPS in production
- Implement rate limiting

## Browser Compatibility
- Chrome (recommended)
- Firefox
- Safari
- Edge

## Support
For questions or issues, please refer to the code comments or create an issue in the project repository.

## License
This project is for educational purposes. Feel free to modify and use as needed.
