<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Modal Test</title>
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="styles/dashboard.css">
</head>
<body>
    <div style="padding: 50px;">
        <h1>Modal Test Page</h1>
        <button class="btn-primary" onclick="testModal()">Test Modal</button>
        
        <!-- Test Modal -->
        <div id="testModal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>Test Modal</h3>
                    <span class="close" onclick="closeTestModal()">&times;</span>
                </div>
                <div style="padding: 20px;">
                    <p>This is a test modal to verify the close button works!</p>
                    <button onclick="closeTestModal()">Close Modal</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        function testModal() {
            document.getElementById('testModal').style.display = 'block';
        }
        
        function closeTestModal() {
            document.getElementById('testModal').style.display = 'none';
        }
        
        // Close modal when clicking outside
        window.onclick = function(event) {
            const modal = document.getElementById('testModal');
            if (event.target === modal) {
                modal.style.display = 'none';
            }
        }
    </script>
</body>
</html>
