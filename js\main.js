// Sample product data (in a real app, this would come from a database)
let products = [
    {
        id: 1,
        name: "Wireless Headphones",
        category: "electronics",
        price: 99.99,
        description: "High-quality wireless headphones with noise cancellation",
        image: "https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=300&h=200&fit=crop",
        stock: 50,
        merchantId: 1
    },
    {
        id: 2,
        name: "Cotton T-Shirt",
        category: "clothing",
        price: 24.99,
        description: "Comfortable 100% cotton t-shirt in various colors",
        image: "https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=300&h=200&fit=crop",
        stock: 100,
        merchantId: 1
    },
    {
        id: 3,
        name: "JavaScript Guide",
        category: "books",
        price: 39.99,
        description: "Complete guide to modern JavaScript development",
        image: "https://images.unsplash.com/photo-1544716278-ca5e3f4abd8c?w=300&h=200&fit=crop",
        stock: 25,
        merchantId: 2
    },
    {
        id: 4,
        name: "Plant Pot Set",
        category: "home",
        price: 34.99,
        description: "Beautiful ceramic plant pots for your home garden",
        image: "https://images.unsplash.com/photo-1485955900006-10f4d324d411?w=300&h=200&fit=crop",
        stock: 30,
        merchantId: 2
    }
];

let filteredProducts = [...products];

// Initialize the page
document.addEventListener('DOMContentLoaded', function() {
    displayProducts();
    updateCartCount();
});

// Display products in the grid
function displayProducts() {
    const productsGrid = document.getElementById('productsGrid');
    productsGrid.innerHTML = '';

    filteredProducts.forEach(product => {
        const productCard = createProductCard(product);
        productsGrid.appendChild(productCard);
    });
}

// Create a product card element
function createProductCard(product) {
    const card = document.createElement('div');
    card.className = 'product-card';
    card.innerHTML = `
        <img src="${product.image}" alt="${product.name}" class="product-image" onerror="this.src='https://via.placeholder.com/300x200?text=No+Image'">
        <div class="product-info">
            <div class="product-name">${product.name}</div>
            <div class="product-price">$${product.price.toFixed(2)}</div>
            <div class="product-description">${product.description}</div>
            <button class="add-to-cart-btn" onclick="addToCart(${product.id})" ${product.stock === 0 ? 'disabled' : ''}>
                ${product.stock === 0 ? 'Out of Stock' : 'Add to Cart'}
            </button>
        </div>
    `;
    return card;
}

// Search products
function searchProducts() {
    const searchTerm = document.getElementById('searchInput').value.toLowerCase();
    const categoryFilter = document.getElementById('categoryFilter').value;
    
    filteredProducts = products.filter(product => {
        const matchesSearch = product.name.toLowerCase().includes(searchTerm) || 
                            product.description.toLowerCase().includes(searchTerm);
        const matchesCategory = !categoryFilter || product.category === categoryFilter;
        
        return matchesSearch && matchesCategory;
    });
    
    displayProducts();
}

// Filter by category
function filterByCategory() {
    searchProducts(); // Reuse the search function which handles both search and category
}

// Scroll to products section
function scrollToProducts() {
    document.getElementById('products').scrollIntoView({ behavior: 'smooth' });
}

// Modal functions
function showLogin() {
    document.getElementById('loginModal').style.display = 'block';
}

function showMerchantLogin() {
    document.getElementById('merchantLoginModal').style.display = 'block';
}

function showRegister() {
    closeModal('loginModal');
    // In a real app, you would show a registration modal here
    alert('Registration functionality would be implemented here');
}

function showMerchantRegister() {
    closeModal('merchantLoginModal');
    // In a real app, you would show a merchant registration modal here
    alert('Merchant registration functionality would be implemented here');
}

function closeModal(modalId) {
    document.getElementById(modalId).style.display = 'none';
}

// Close modal when clicking outside
window.onclick = function(event) {
    const modals = document.querySelectorAll('.modal');
    modals.forEach(modal => {
        if (event.target === modal) {
            modal.style.display = 'none';
        }
    });
}

// Form submissions
document.getElementById('loginForm').addEventListener('submit', function(e) {
    e.preventDefault();
    // In a real app, you would handle authentication here
    alert('Login functionality would be implemented here');
    closeModal('loginModal');
});

document.getElementById('merchantLoginForm').addEventListener('submit', function(e) {
    e.preventDefault();
    // In a real app, you would handle merchant authentication here
    // For demo purposes, redirect to dashboard
    window.location.href = 'merchant-dashboard.html';
});

// Utility functions
function formatCurrency(amount) {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
    }).format(amount);
}

function generateId() {
    return Date.now() + Math.random().toString(36).substr(2, 9);
}

// Export functions for use in other files
window.products = products;
window.displayProducts = displayProducts;
window.searchProducts = searchProducts;
window.filterByCategory = filterByCategory;
window.scrollToProducts = scrollToProducts;
window.showLogin = showLogin;
window.showMerchantLogin = showMerchantLogin;
window.showRegister = showRegister;
window.showMerchantRegister = showMerchantRegister;
window.closeModal = closeModal;
window.formatCurrency = formatCurrency;
window.generateId = generateId;
