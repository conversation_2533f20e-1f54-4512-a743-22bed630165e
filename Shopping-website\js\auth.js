// Authentication system
let currentUser = JSON.parse(localStorage.getItem('currentUser')) || null;
let currentMerchant = JSON.parse(localStorage.getItem('currentMerchant')) || null;

// Sample users and merchants (in a real app, this would be in a database)
const users = [
    { id: 1, email: '<EMAIL>', password: 'password123', name: '<PERSON>' },
    { id: 2, email: '<EMAIL>', password: 'password123', name: '<PERSON>' }
];

const merchants = [
    { id: 1, email: '<EMAIL>', password: 'merchant123', name: 'Tech Store', businessName: 'Tech Solutions Inc.' },
    { id: 2, email: '<EMAIL>', password: 'books123', name: 'Book Store', businessName: 'Literary World' }
];

// Initialize auth state
document.addEventListener('DOMContentLoaded', function() {
    updateAuthUI();
});

// Customer login
function loginUser(email, password) {
    const user = users.find(u => u.email === email && u.password === password);
    
    if (user) {
        currentUser = user;
        localStorage.setItem('currentUser', JSON.stringify(user));
        updateAuthUI();
        return true;
    }
    
    return false;
}

// Merchant login
function loginMerchant(email, password) {
    const merchant = merchants.find(m => m.email === email && m.password === password);
    
    if (merchant) {
        currentMerchant = merchant;
        localStorage.setItem('currentMerchant', JSON.stringify(merchant));
        return true;
    }
    
    return false;
}

// Logout
function logout() {
    currentUser = null;
    currentMerchant = null;
    localStorage.removeItem('currentUser');
    localStorage.removeItem('currentMerchant');
    
    // Redirect to home if on dashboard
    if (window.location.pathname.includes('merchant-dashboard.html')) {
        window.location.href = 'index.html';
    } else {
        updateAuthUI();
    }
}

// Update UI based on auth state
function updateAuthUI() {
    const navMenu = document.querySelector('.nav-menu');
    if (!navMenu) return;
    
    // Find login and merchant links
    const loginLink = navMenu.querySelector('a[onclick="showLogin()"]');
    const merchantLink = navMenu.querySelector('a[onclick="showMerchantLogin()"]');
    
    if (currentUser) {
        // User is logged in
        if (loginLink) {
            loginLink.textContent = currentUser.name;
            loginLink.onclick = null;
            loginLink.href = '#';
        }
        
        // Add logout option
        if (!navMenu.querySelector('.logout-link')) {
            const logoutLink = document.createElement('a');
            logoutLink.href = '#';
            logoutLink.className = 'nav-link logout-link';
            logoutLink.textContent = 'Logout';
            logoutLink.onclick = logout;
            navMenu.insertBefore(logoutLink, navMenu.querySelector('.cart-icon'));
        }
    } else if (currentMerchant) {
        // Merchant is logged in (for dashboard page)
        const merchantName = document.getElementById('merchantName');
        if (merchantName) {
            merchantName.textContent = `Welcome, ${currentMerchant.businessName}`;
        }
    }
}

// Check if user is authenticated
function isAuthenticated() {
    return currentUser !== null;
}

// Check if merchant is authenticated
function isMerchantAuthenticated() {
    return currentMerchant !== null;
}

// Get current user
function getCurrentUser() {
    return currentUser;
}

// Get current merchant
function getCurrentMerchant() {
    return currentMerchant;
}

// Register new user (simplified)
function registerUser(email, password, name) {
    // Check if user already exists
    if (users.find(u => u.email === email)) {
        return { success: false, message: 'User already exists' };
    }
    
    const newUser = {
        id: users.length + 1,
        email,
        password,
        name
    };
    
    users.push(newUser);
    
    // Auto-login after registration
    currentUser = newUser;
    localStorage.setItem('currentUser', JSON.stringify(newUser));
    updateAuthUI();
    
    return { success: true, message: 'Registration successful' };
}

// Register new merchant (simplified)
function registerMerchant(email, password, name, businessName) {
    // Check if merchant already exists
    if (merchants.find(m => m.email === email)) {
        return { success: false, message: 'Merchant already exists' };
    }
    
    const newMerchant = {
        id: merchants.length + 1,
        email,
        password,
        name,
        businessName
    };
    
    merchants.push(newMerchant);
    
    return { success: true, message: 'Merchant registration successful' };
}

// Enhanced form handlers
if (document.getElementById('loginForm')) {
    document.getElementById('loginForm').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const email = this.querySelector('input[type="email"]').value;
        const password = this.querySelector('input[type="password"]').value;
        
        if (loginUser(email, password)) {
            closeModal('loginModal');
            showNotification('Login successful!');
        } else {
            alert('Invalid email or password');
        }
    });
}

if (document.getElementById('merchantLoginForm')) {
    document.getElementById('merchantLoginForm').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const email = this.querySelector('input[type="email"]').value;
        const password = this.querySelector('input[type="password"]').value;
        
        if (loginMerchant(email, password)) {
            window.location.href = 'merchant-dashboard.html';
        } else {
            alert('Invalid merchant credentials');
        }
    });
}

// Protect merchant dashboard
if (window.location.pathname.includes('merchant-dashboard.html')) {
    if (!isMerchantAuthenticated()) {
        alert('Please login as a merchant to access the dashboard');
        window.location.href = 'index.html';
    } else {
        updateAuthUI();
    }
}

// Export functions
window.loginUser = loginUser;
window.loginMerchant = loginMerchant;
window.logout = logout;
window.isAuthenticated = isAuthenticated;
window.isMerchantAuthenticated = isMerchantAuthenticated;
window.getCurrentUser = getCurrentUser;
window.getCurrentMerchant = getCurrentMerchant;
window.registerUser = registerUser;
window.registerMerchant = registerMerchant;
