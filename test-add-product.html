<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Add Product</title>
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="styles/dashboard.css">
</head>
<body>
    <div style="padding: 20px;">
        <h1>Test Add Product Functionality</h1>
        <button onclick="testAddProduct()" class="btn-primary">Test Add Product</button>
        <button onclick="showProducts()" class="btn-primary">Show Products</button>
        
        <div id="output" style="margin-top: 20px; padding: 20px; background: #f8f9fa; border-radius: 5px;">
            <h3>Output:</h3>
            <pre id="result"></pre>
        </div>
    </div>

    <script src="js/main.js"></script>
    <script src="js/auth.js"></script>
    <script>
        // Mock merchant login
        const mockMerchant = { id: 1, email: '<EMAIL>', name: 'Test Merchant' };
        localStorage.setItem('currentMerchant', JSON.stringify(mockMerchant));
        
        function testAddProduct() {
            const result = document.getElementById('result');
            
            // Test product data
            const testProduct = {
                id: Date.now(),
                name: 'Test Product',
                category: 'electronics',
                price: 99.99,
                stock: 10,
                description: 'This is a test product',
                image: 'https://via.placeholder.com/300x200?text=Test+Product',
                merchantId: 1
            };
            
            try {
                // Add to global products array
                if (!window.products) {
                    window.products = [];
                }
                
                window.products.push(testProduct);
                localStorage.setItem('products', JSON.stringify(window.products));
                
                result.textContent = 'SUCCESS: Product added!\n' + JSON.stringify(testProduct, null, 2);
                result.style.color = 'green';
            } catch (error) {
                result.textContent = 'ERROR: ' + error.message;
                result.style.color = 'red';
            }
        }
        
        function showProducts() {
            const result = document.getElementById('result');
            const products = JSON.parse(localStorage.getItem('products')) || [];
            
            result.textContent = 'Current Products (' + products.length + '):\n' + JSON.stringify(products, null, 2);
            result.style.color = 'blue';
        }
        
        // Show current state on load
        window.onload = function() {
            showProducts();
        };
    </script>
</body>
</html>
