<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ShopEasy - Online Shopping</title>
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <h2>ShopEasy</h2>
            </div>
            <div class="nav-menu">
                <a href="#home" class="nav-link">Home</a>
                <a href="#products" class="nav-link">Products</a>
                <a href="#" class="nav-link" onclick="showLogin()">Login</a>
                <a href="#" class="nav-link" onclick="showMerchantLogin()">Merchant</a>
                <div class="cart-icon" onclick="toggleCart()">
                    <i class="fas fa-shopping-cart"></i>
                    <span class="cart-count" id="cartCount">0</span>
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section id="home" class="hero">
        <div class="hero-content">
            <h1>Welcome to ShopEasy</h1>
            <p>Discover amazing products from trusted merchants worldwide</p>
            <button class="cta-button float" onclick="scrollToProducts()">
                <i class="fas fa-shopping-bag"></i> Shop Now
            </button>
        </div>
    </section>

    <!-- Products Section -->
    <section id="products" class="products-section">
        <div class="container">
            <h2>✨ Featured Products</h2>
            <div class="search-filter">
                <input type="text" id="searchInput" placeholder="🔍 Search for amazing products..." onkeyup="searchProducts()">
                <select id="categoryFilter" onchange="filterByCategory()">
                    <option value="">🏷️ All Categories</option>
                    <option value="electronics">📱 Electronics</option>
                    <option value="clothing">👕 Clothing</option>
                    <option value="books">📚 Books</option>
                    <option value="home">🏠 Home & Garden</option>
                </select>
            </div>
            <div class="products-grid" id="productsGrid">
                <!-- Products will be loaded here -->
            </div>
        </div>
    </section>

    <!-- Shopping Cart -->
    <div id="cartModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Shopping Cart</h3>
                <span class="close" onclick="toggleCart()">&times;</span>
            </div>
            <div class="cart-items" id="cartItems">
                <!-- Cart items will be displayed here -->
            </div>
            <div class="cart-total">
                <h4>Total: $<span id="cartTotal">0.00</span></h4>
                <button class="checkout-btn" onclick="checkout()">Checkout</button>
            </div>
        </div>
    </div>

    <!-- Login Modal -->
    <div id="loginModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Customer Login</h3>
                <span class="close" onclick="closeModal('loginModal')">&times;</span>
            </div>
            <form id="loginForm">
                <input type="email" placeholder="Email" required>
                <input type="password" placeholder="Password" required>
                <button type="submit">Login</button>
                <p>Don't have an account? <a href="#" onclick="showRegister()">Register</a></p>
            </form>
        </div>
    </div>

    <!-- Merchant Login Modal -->
    <div id="merchantLoginModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Merchant Login</h3>
                <span class="close" onclick="closeModal('merchantLoginModal')">&times;</span>
            </div>
            <form id="merchantLoginForm">
                <input type="email" placeholder="Merchant Email" required>
                <input type="password" placeholder="Password" required>
                <button type="submit">Login</button>
                <p>New merchant? <a href="#" onclick="showMerchantRegister()">Register</a></p>
            </form>
        </div>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <p>&copy; 2025 ShopEasy. All rights reserved to Aditya Chaudhary (admin) .</p>
        </div>
    </footer>

    <script src="js/main.js"></script>
    <script src="js/cart.js"></script>
    <script src="js/auth.js"></script>
</body>
</html>
